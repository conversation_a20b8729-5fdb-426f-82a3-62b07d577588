<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jeopardy Game - Module 1 Review: Lifestyles</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            border-radius: 15px;
        }

        .scoreboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 10px;
        }

        .team-score {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .team-score.active {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-color: #004085;
            transform: scale(1.05);
        }

        .team-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .team-points {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }

        .team-score.active .team-points {
            color: #fff;
        }

        .team-controls {
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .point-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .add-points {
            background: #28a745;
            color: white;
        }

        .subtract-points {
            background: #dc3545;
            color: white;
        }

        .point-btn:hover {
            transform: scale(1.1);
        }

        .next-team-container {
            text-align: center;
            margin: 15px 0 30px 0;
        }

        .next-team-btn {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .next-team-btn:hover {
            background: #e0a800;
            transform: scale(1.05);
        }

        .jeopardy-board {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            background: #2c3e50;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .category-header {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            border-radius: 10px;
            font-size: 14px;
        }

        .question-cell {
            background: #e74c3c;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-cell:hover {
            background: #c0392b;
            transform: scale(1.05);
        }

        .question-cell.used {
            background: #7f8c8d;
            cursor: not-allowed;
            opacity: 0.5;
        }

        .question-cell.used:hover {
            background: #7f8c8d;
            transform: none;
        }

        .question-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            z-index: 1000;
        }

        .question-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .question-text {
            font-size: 24px;
            margin-bottom: 30px;
            color: #2c3e50;
            line-height: 1.4;
        }

        .timer-display {
            font-size: 48px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 30px;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .option-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            text-align: left;
        }

        .option-btn:hover {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        .option-btn.selected {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        .option-btn.correct {
            background: #28a745;
            color: white;
            border-color: #1e7e34;
        }

        .option-btn.wrong {
            background: #dc3545;
            color: white;
            border-color: #bd2130;
        }

        .popup-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .popup-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .popup-btn:hover {
            background: #0056b3;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .current-turn {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            border-radius: 15px;
            color: #2d3436;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .control-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .control-btn:hover {
            background: #218838;
        }

        .final-scores {
            text-align: center;
            padding: 30px;
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>🎯 Jeopardy Game</h1>
            <p>Module 1 Review: City vs. Country, Personality, Jobs, Grammar, Everyday English</p>
            <p><strong>4 Teams • 30 Second Timer • Multiple Choice Questions</strong></p>
        </div>

        <div class="scoreboard" id="scoreboard">
            <!-- Team scores will be generated here -->
        </div>

        <div class="next-team-container">
            <button class="next-team-btn" onclick="nextTeam()">➡️ Next Team's Turn</button>
        </div>

        <div class="current-turn" id="currentTurn">
            🎯 Team 1's Turn - Choose a category and points!
        </div>

        <div class="jeopardy-board" id="jeopardyBoard">
            <!-- Game board will be generated here -->
        </div>

        <div class="game-controls">
            <button class="control-btn" onclick="resetGame()">🔄 New Game</button>
            <button class="control-btn" onclick="showFinalScores()">🏆 Final Scores</button>
            <button class="control-btn" onclick="window.close()">✅ Finish Game</button>
        </div>

        <div class="final-scores" id="finalScores">
            <!-- Final scores will be shown here -->
        </div>
    </div>

    <!-- Question Popup -->
    <div class="question-popup" id="questionPopup">
        <div class="question-content">
            <div class="timer-display" id="timerDisplay">30</div>
            <div class="question-text" id="questionText"></div>
            <div class="options-grid" id="optionsGrid"></div>
            <div class="popup-controls">
                <button class="popup-btn back-btn" onclick="closeQuestion()">⬅️ Back</button>
            </div>
        </div>
    </div>

    <script>
        const teams = [
            { name: 'Team 1', score: 0, active: true },
            { name: 'Team 2', score: 0, active: false },
            { name: 'Team 3', score: 0, active: false },
            { name: 'Team 4', score: 0, active: false }
        ];

        const categories = [
            'City vs Country',
            'Personality',
            'Jobs',
            'Grammar',
            'Everyday English'
        ];

        const questions = {
            'City vs Country': {
                100: { q: "Which place usually has crowded streets and heavy traffic?", options: ["Country", "City", "Village", "Farm"], correct: 1 },
                200: { q: "Which place is known for fresh air and beautiful countryside?", options: ["City", "Country", "Airport", "Factory"], correct: 1 },
                300: { q: "Which is a city convenience?", options: ["Barn", "Milk the cows", "Public transportation", "Feed the hens"], correct: 2 },
                400: { q: "Which phrase matches the city?", options: ["Quiet and peaceful", "Constant noise and pollution", "Friendly barns", "Maple syrup"], correct: 1 },
                500: { q: "Meg feels excited in New York because she can...", options: ["Smell the barn", "See a Broadway musical", "Milk cows", "Make maple syrup"], correct: 1 }
            },
            'Personality': {
                100: { q: "Brave is the opposite of...", options: ["Honest", "Lazy", "Cowardly", "Polite"], correct: 2 },
                200: { q: "Friendly is the opposite of...", options: ["Impolite", "Unfriendly", "Careless", "Dishonest"], correct: 1 },
                300: { q: "Hardworking is the opposite of...", options: ["Lazy", "Patient", "Careful", "Skillful"], correct: 0 },
                400: { q: "Which pair is correct?", options: ["Polite – Careless", "Honest – Dishonest", "Patient – Careful", "Skillful – Shy"], correct: 1 },
                500: { q: "Imaginative is the opposite of...", options: ["Unimaginative", "Impulsive", "Impolite", "Unskilled"], correct: 0 }
            },
            'Jobs': {
                100: { q: "Who takes photos of animals?", options: ["Teacher", "Wildlife photographer", "Chef", "Police officer"], correct: 1 },
                200: { q: "Who reports news from dangerous places?", options: ["War reporter", "Farmer", "Singer", "Doctor"], correct: 0 },
                300: { q: "A storm chaser’s job can be described as...", options: ["Safe", "Relaxing", "Dangerous", "Boring"], correct: 2 },
                400: { q: "Which job works in a restaurant?", options: ["Chef", "Pilot", "Farmer", "Photographer"], correct: 0 },
                500: { q: "Which sentence is true?", options: ["War reporters must be cowardly.", "Wildlife photographers never travel.", "Storm chasers follow extreme weather.", "Teachers work in hospitals."], correct: 2 }
            },
            'Grammar': {
                100: { q: "Form of present progressive: I ___ (make) breakfast.", options: ["make", "am making", "is making", "are make"], correct: 1 },
                200: { q: "Simple present vs. present progressive: She usually ___ (take) the subway.", options: ["is taking", "take", "takes", "are taking"], correct: 2 },
                300: { q: "Relative clause: I like people ___ are honest.", options: ["which", "whose", "who", "where"], correct: 2 },
                400: { q: "Adjective from noun: danger → ___", options: ["danger", "dangerly", "dangerous", "dangerful"], correct: 2 },
                500: { q: "Choose the place clause: The café ___ we meet.", options: ["who", "whose", "which", "where"], correct: 3 }
            },
            'Everyday English': {
                100: { q: "At a ticket office: 'Next, please. ___?'", options: ["Who’s next", "Where to", "How old", "Why you"], correct: 1 },
                200: { q: "Choose the phrase for tickets:", options: ["Round-trip, please.", "Two sandwiches, please.", "One-way street.", "Close at hand."], correct: 0 },
                300: { q: "Complete: 'That’s ___.'", options: ["$178", "late", "tomorrow", "number"], correct: 0 },
                400: { q: "Word meaning 'nearby':", options: ["Homesick", "Isolated", "Close at hand", "Crowded"], correct: 2 },
                500: { q: "Dialogue question: '___ or round-trip?'", options: ["One-way", "Who", "Where", "How"], correct: 0 }
            }
        };

        let currentTeamIndex = 0;
        let gameActive = true;
        let questionTimer;
        let timeLeft = 30;
        let currentQuestion = null;

        function initializeGame() {
            createBoard();
            updateScoreboard();
            updateCurrentTurn();
        }

        function createBoard() {
            const board = document.getElementById('jeopardyBoard');
            board.innerHTML = '';

            // Create category headers
            categories.forEach(category => {
                const header = document.createElement('div');
                header.className = 'category-header';
                header.textContent = category;
                board.appendChild(header);
            });

            // Create question cells
            const points = [100, 200, 300, 400, 500];
            points.forEach(point => {
                categories.forEach(category => {
                    const cell = document.createElement('div');
                    cell.className = 'question-cell';
                    cell.textContent = point;
                    cell.onclick = () => openQuestion(category, point);
                    cell.setAttribute('data-category', category);
                    cell.setAttribute('data-points', point);
                    board.appendChild(cell);
                });
            });
        }

        function openQuestion(category, points) {
            const cell = document.querySelector(`[data-category="${category}"][data-points="${points}"]`);
            
            currentQuestion = { category, points, cell };
            const questionData = questions[category][points];

            // Show popup
            document.getElementById('questionText').textContent = questionData.q;
            
            // Create options
            const optionsGrid = document.getElementById('optionsGrid');
            optionsGrid.innerHTML = '';
            questionData.options.forEach((option, index) => {
                const optionBtn = document.createElement('div');
                optionBtn.className = 'option-btn';
                optionBtn.textContent = `${String.fromCharCode(65 + index)}. ${option}`;
                optionBtn.onclick = () => selectOption(index);
                optionsGrid.appendChild(optionBtn);
            });

            // Start timer
            startTimer();
            
            // Show popup
            document.getElementById('questionPopup').style.display = 'block';
        }

        function selectOption(selectedIndex) {
            clearInterval(questionTimer);
            const questionData = questions[currentQuestion.category][currentQuestion.points];
            const options = document.querySelectorAll('.option-btn');
            
            if (selectedIndex === questionData.correct) {
                // If correct, show green for the selected answer
                options[selectedIndex].classList.add('correct');
                options.forEach((option, index) => {
                    if (index !== selectedIndex) {
                        option.style.opacity = '0.5';
                    }
                });
                
                teams[currentTeamIndex].score += currentQuestion.points;
                updateScoreboard();
                currentQuestion.cell.classList.add('used');
                currentQuestion.cell.textContent = 'USED';
            } else {
                // If wrong, only show red for the selected answer
                options[selectedIndex].classList.add('wrong');
                options.forEach((option, index) => {
                    if (index !== selectedIndex) {
                        option.style.opacity = '0.5';
                    }
                });
            }
            
            // Always move to next team after 1.5 seconds
            setTimeout(() => {
                nextTeam();
            }, 1500);
        }

        function startTimer() {
            timeLeft = 30;
            document.getElementById('timerDisplay').textContent = timeLeft;
            
            questionTimer = setInterval(() => {
                timeLeft--;
                document.getElementById('timerDisplay').textContent = timeLeft;
                
                if (timeLeft <= 0) {
                    clearInterval(questionTimer);
                    alert('⏰ Time\'s up! Moving to next team.');
                    nextTeam();
                }
            }, 1000);
        }

        function closeQuestion() {
            clearInterval(questionTimer);
            document.getElementById('questionPopup').style.display = 'none';
        }

        function nextTeam() {
            closeQuestion();
            
            // Switch to next team
            teams[currentTeamIndex].active = false;
            currentTeamIndex = (currentTeamIndex + 1) % 4;
            teams[currentTeamIndex].active = true;
            
            updateScoreboard();
            updateCurrentTurn();
        }

        function updateScoreboard() {
            const scoreboard = document.getElementById('scoreboard');
            scoreboard.innerHTML = '';
            
            teams.forEach((team, index) => {
                const teamDiv = document.createElement('div');
                teamDiv.className = `team-score ${team.active ? 'active' : ''}`;
                teamDiv.innerHTML = `
                    <div class="team-name">${team.name}</div>
                    <div class="team-points">${team.score} pts</div>
                    <div class="team-controls">
                        <button class="point-btn add-points" onclick="adjustPoints(${index}, 10)">+10</button>
                        <button class="point-btn subtract-points" onclick="adjustPoints(${index}, -10)">-10</button>
                    </div>
                `;
                scoreboard.appendChild(teamDiv);
            });
        }

        function adjustPoints(teamIndex, points) {
            teams[teamIndex].score += points;
            updateScoreboard();
        }

        function updateCurrentTurn() {
            const currentTeam = teams[currentTeamIndex];
            document.getElementById('currentTurn').textContent = 
                `🎯 ${currentTeam.name}'s Turn - Choose a category and points!`;
        }

        function showFinalScores() {
            const sortedTeams = [...teams].sort((a, b) => b.score - a.score);
            const finalScoresDiv = document.getElementById('finalScores');
            
            let html = '<h2>🏆 Final Scores</h2>';
            sortedTeams.forEach((team, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏅';
                html += `<div style="margin: 10px 0; font-size: 20px;">${medal} ${team.name}: ${team.score} points</div>`;
            });
            
            html += '<button class="control-btn" onclick="resetGame()" style="margin-top: 20px;">🎮 Play Again</button>';
            
            finalScoresDiv.innerHTML = html;
            finalScoresDiv.style.display = 'block';
        }

        function resetGame() {
            // Reset teams
            teams.forEach((team, index) => {
                team.score = 0;
                team.active = index === 0;
            });
            
            currentTeamIndex = 0;
            gameActive = true;
            clearInterval(questionTimer);
            
            // Hide final scores
            document.getElementById('finalScores').style.display = 'none';
            document.getElementById('questionPopup').style.display = 'none';
            
            // Recreate board
            createBoard();
            updateScoreboard();
            updateCurrentTurn();
        }

        // Initialize game when page loads
        window.onload = function() {
            initializeGame();
        };
    </script>
</body>
</html> 