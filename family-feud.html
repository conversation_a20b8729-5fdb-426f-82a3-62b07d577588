<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Feud - Module 1 Review: Lifestyles</title>
    <style>
        body {
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #FFB6C1 0%, #87CEEB 100%);
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #FFA07A, #FFB6C1);
            color: #333;
            border-radius: 15px;
            border: 3px solid #FFF;
        }

        .question-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            padding: 25px;
            background: #E6F3FF;
            border: 3px dashed #87CEEB;
            border-radius: 15px;
            color: #333;
        }

        .answers-board {
            background: #F0F8FF;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 3px solid #87CEEB;
        }

        .answer-slot {
            background: #FFF;
            border: 3px solid #FFB6C1;
            border-radius: 12px;
            margin: 10px 0;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .answer-slot:hover {
            background: #FFE4E1;
            transform: scale(1.02);
        }

        .answer-slot.revealed {
            background: linear-gradient(45deg, #98FB98, #90EE90);
            border-color: #3CB371;
            color: #333;
            cursor: default;
        }

        .answer-slot.revealed:hover {
            background: linear-gradient(45deg, #98FB98, #90EE90);
        }

        .answer-number {
            font-size: 24px;
            font-weight: bold;
            color: #ecf0f1;
            width: 40px;
        }

        .answer-text {
            flex-grow: 1;
            font-size: 20px;
            font-weight: bold;
            color: #ecf0f1;
            text-align: center;
            letter-spacing: 2px;
        }

        .answer-slot.revealed .answer-text {
            color: white;
            letter-spacing: normal;
        }

        .answer-points {
            font-size: 24px;
            font-weight: bold;
            color: #f39c12;
            width: 60px;
            text-align: right;
        }

        .answer-slot.revealed .answer-points {
            color: #fff;
        }

        .hidden-answer {
            visibility: hidden;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .control-btn {
            background: #87CEEB;
            color: #333;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            border: 2px solid #FFF;
        }

        .control-btn:hover {
            background: #FFB6C1;
            transform: scale(1.05);
        }

        .reveal-next-btn {
            background: #28a745;
        }

        .reveal-next-btn:hover {
            background: #218838;
        }

        .reveal-all-btn {
            background: #dc3545;
        }

        .reveal-all-btn:hover {
            background: #c82333;
        }

        .reset-btn {
            background: #6c757d;
        }

        .reset-btn:hover {
            background: #545b62;
        }

        .category-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .category-btn {
            background: #FFF;
            border: 3px solid #87CEEB;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
            color: #333;
        }

        .category-btn:hover {
            background: #E6F3FF;
            border-color: #FFB6C1;
            transform: scale(1.05);
        }

        .category-btn.active {
            background: #FFB6C1;
            color: #333;
            border-color: #FFA07A;
        }

        .total-score {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            border-radius: 15px;
            color: #2d3436;
        }

        .game-instructions {
            background: #FFF5EE;
            border: 3px dashed #FFB6C1;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .game-instructions h3 {
            color: #FF69B4;
            margin-bottom: 15px;
        }

        .game-instructions p {
            margin: 10px 0;
            font-size: 18px;
            color: #333;
        }

        .strike-system {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }

        .strike {
            font-size: 48px;
            color: #e74c3c;
            opacity: 0.3;
            transition: opacity 0.3s ease;
        }

        .strike.active {
            opacity: 1;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .completion-message {
            text-align: center;
            padding: 30px;
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
            font-size: 20px;
            font-weight: bold;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>Module 1 Review: Lifestyles</h1>
            <p>Click the boxes to reveal vocabulary and phrases from the unit!</p>
            <p><strong>Find items worth: 5 • 15 • 20 • 25 • 30 points</strong></p>
        </div>

        <div class="game-instructions">
            <h3>👋 How to Play</h3>
            <p>1. Pick a category from the unit</p>
            <p>2. Click boxes to reveal correct items</p>
            <p>3. Try to find all the words/phrases!</p>
        </div>

        <div class="category-selector" id="categorySelector">
            <!-- Category buttons will be generated here -->
        </div>

        <div class="question-display" id="questionDisplay">
            Select a category to start playing!
        </div>

        <div class="strike-system" id="strikeSystem" style="display: none;">
            <div>Strikes:</div>
            <div class="strike" id="strike1">❌</div>
            <div class="strike" id="strike2">❌</div>
            <div class="strike" id="strike3">❌</div>
        </div>

        <div class="answers-board" id="answersBoard">
            <!-- Answer slots will be generated here -->
        </div>

        <div class="total-score" id="totalScore">
            Total Score: 0 points
        </div>

        <div class="controls">
            <button class="control-btn reveal-all-btn" onclick="revealAll()">⚡ Reveal All</button>
            <button class="control-btn reset-btn" onclick="resetRound()">🔄 New Round</button>
            <button class="control-btn" onclick="window.close()">✅ Finish Game</button>
        </div>

        <div class="completion-message" id="completionMessage">
            <!-- Completion message will appear here -->
        </div>
    </div>

    <script>
        const gameData = {
            'City Life Words': {
                question: 'Find words that describe the city.',
                answers: [
                    { text: 'Modern buildings', points: 30 },
                    { text: 'Crowded streets', points: 25 },
                    { text: 'Heavy traffic', points: 20 },
                    { text: 'Public transportation', points: 15 },
                    { text: 'Pollution', points: 5 }
                ]
            },
            'Country Life Words': {
                question: 'Find words that describe the country.',
                answers: [
                    { text: 'Quiet', points: 30 },
                    { text: 'Fresh air', points: 25 },
                    { text: 'Beautiful countryside', points: 20 },
                    { text: 'Friendly/helpful people', points: 15 },
                    { text: 'Barn', points: 5 }
                ]
            },
            'Personality Opposites': {
                question: 'Find correct adjective pairs (A – opposite).',
                answers: [
                    { text: 'Brave – Cowardly', points: 30 },
                    { text: 'Friendly – Unfriendly', points: 25 },
                    { text: 'Hardworking – Lazy', points: 20 },
                    { text: 'Patient – Impatient', points: 15 },
                    { text: 'Honest – Dishonest', points: 5 }
                ]
            },
            'Jobs': {
                question: 'Find jobs from the unit.',
                answers: [
                    { text: 'War reporter', points: 30 },
                    { text: 'Wildlife photographer', points: 25 },
                    { text: 'Storm chaser', points: 20 },
                    { text: 'Teacher', points: 15 },
                    { text: 'Chef', points: 5 }
                ]
            },
            'Ticket Dialog Phrases': {
                question: 'Find phrases used when buying a ticket.',
                answers: [
                    { text: 'Next, please.', points: 30 },
                    { text: 'Where to?', points: 25 },
                    { text: 'One-way or round-trip?', points: 20 },
                    { text: "That’s $178.", points: 15 },
                    { text: 'Thanks a lot.', points: 5 }
                ]
            }
        };

        let currentCategory = '';
        let currentAnswers = [];
        let revealedCount = 0;
        let totalScore = 0;
        let strikes = 0;
        let nextAnswerIndex = 0;  // Track which answer should be revealed next

        function initializeGame() {
            createCategorySelector();
        }

        function createCategorySelector() {
            const selector = document.getElementById('categorySelector');
            selector.innerHTML = '';
            
            Object.keys(gameData).forEach(category => {
                const btn = document.createElement('div');
                btn.className = 'category-btn';
                btn.textContent = category;
                btn.onclick = () => selectCategory(category);
                selector.appendChild(btn);
            });
        }

        function selectCategory(category) {
            currentCategory = category;
            currentAnswers = [...gameData[category].answers];
            revealedCount = 0;
            strikes = 0;
            
            // Update category button states
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent === category) {
                    btn.classList.add('active');
                }
            });
            
            // Show question
            document.getElementById('questionDisplay').textContent = gameData[category].question;
            
            // Show strike system
            document.getElementById('strikeSystem').style.display = 'flex';
            updateStrikes();
            
            // Create answer board
            createAnswerBoard();
            
            // Hide completion message
            document.getElementById('completionMessage').style.display = 'none';
        }

        function createAnswerBoard() {
            const board = document.getElementById('answersBoard');
            board.innerHTML = '';
            
            currentAnswers.forEach((answer, index) => {
                const slot = document.createElement('div');
                slot.className = 'answer-slot';
                slot.onclick = () => revealAnswer(index);  // Add back click handler
                
                slot.innerHTML = `
                    <div class="answer-number">${index + 1}</div>
                    <div class="answer-text hidden-answer">${answer.text}</div>
                    <div class="answer-points hidden-answer">${answer.points}</div>
                `;
                
                board.appendChild(slot);
            });
        }

        function revealAnswer(index) {
            const slots = document.querySelectorAll('.answer-slot');
            const slot = slots[index];
            
            if (!slot.classList.contains('revealed')) {
                slot.classList.add('revealed');
                const answerText = slot.querySelector('.answer-text');
                const answerPoints = slot.querySelector('.answer-points');
                answerText.classList.remove('hidden-answer');
                answerPoints.classList.remove('hidden-answer');
                
                totalScore += currentAnswers[index].points;
                updateTotalScore();
                
                revealedCount++;
                
                // Check if all answers revealed
                if (revealedCount === currentAnswers.length) {
                    setTimeout(() => {
                        showCompletionMessage();
                    }, 1000);
                }
            }
        }

        function updateTotalScore() {
            document.getElementById('totalScore').textContent = `Total Score: ${totalScore} points`;
        }

        function updateStrikes() {
            for (let i = 1; i <= 3; i++) {
                const strike = document.getElementById(`strike${i}`);
                if (i <= strikes) {
                    strike.classList.add('active');
                } else {
                    strike.classList.remove('active');
                }
            }
        }

        function addStrike() {
            strikes++;
            updateStrikes();
            
            if (strikes >= 3) {
                setTimeout(() => {
                    alert('💥 3 Strikes! Game Over for this round!');
                    revealAll();
                }, 500);
            }
        }

        function revealAll() {
            const slots = document.querySelectorAll('.answer-slot');
            let delay = 0;
            
            slots.forEach((slot, index) => {
                if (!slot.classList.contains('revealed')) {
                    setTimeout(() => {
                        slot.classList.add('revealed');
                        const answerText = slot.querySelector('.answer-text');
                        const answerPoints = slot.querySelector('.answer-points');
                        answerText.classList.remove('hidden-answer');
                        answerPoints.classList.remove('hidden-answer');
                        
                        totalScore += currentAnswers[index].points;
                        updateTotalScore();
                    }, delay);
                    delay += 500;
                }
            });
            
            setTimeout(() => {
                showCompletionMessage();
            }, delay + 500);
        }

        function showCompletionMessage() {
            const message = document.getElementById('completionMessage');
            let performanceMsg = '';
            
            if (totalScore >= 90) {
                performanceMsg = '🌟 Wow! You did amazing!';
            } else if (totalScore >= 70) {
                performanceMsg = '👍 Great job! You know so many fun activities!';
            } else if (totalScore >= 50) {
                performanceMsg = '😊 Well done! Keep playing to learn more!';
            } else {
                performanceMsg = '💪 Good try! Let\'s play again!';
            }
            
            message.innerHTML = `
                <h2>🎉 You Did It!</h2>
                <div style="font-size: 36px; margin: 20px 0;">You got ${totalScore} points!</div>
                <p>${performanceMsg}</p>
                <button class="control-btn" onclick="resetRound()" style="margin-top: 15px;">
                    🎮 Play Again
                </button>
            `;
            
            message.style.display = 'block';
        }

        function resetRound() {
            totalScore = 0;
            revealedCount = 0;
            strikes = 0;
            currentCategory = '';
            
            updateTotalScore();
            
            // Reset category selector
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Reset displays
            document.getElementById('questionDisplay').textContent = 'Select a category to start playing!';
            document.getElementById('answersBoard').innerHTML = '';
            document.getElementById('strikeSystem').style.display = 'none';
            document.getElementById('completionMessage').style.display = 'none';
        }

        // Remove keyboard support for revealing answers since we want manual control
        document.removeEventListener('keydown', function(event) {
            if (currentCategory && event.key >= '1' && event.key <= '5') {
                const index = parseInt(event.key) - 1;
                if (index < currentAnswers.length) {
                    revealAnswer(index);
                }
            }
        });

        // Initialize game when page loads
        window.onload = function() {
            initializeGame();
        };
    </script>
</body>
</html> 